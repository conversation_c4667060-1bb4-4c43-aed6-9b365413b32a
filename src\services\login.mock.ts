import { axiosRes } from '@/mock/res';
import { HttpResponse, http } from 'msw';

export const commonInfo = {
  'tenants': [
    {
      'tenantId': ***************,
      'tenantName': 'react-antd-console',
      'contactName': '大饼',
      'contactMobile': '***********',
      'contactEmail': 'react-antd-console.site',
      'remark': '',
    },
  ],
  'tenantId': ***************,
};

export const userAdmin = {
  'userAccount': 'admin',
  'userId': ***************,
  'avatar': '',
  'roleId': ***************,
  'permissions': [
    'home',
    'home:index',
    'home:alive',
    'home:grid',
    'profile',

    'permission',
    'permission:route',
    'permission:local',
    'permission:local:btn1',
    'permission:local:btn2',

    'router',
    'router:dynamic',
    'router:meta',

    'tablePage',
    'tablePage:tablePage',
    'tablePage:tablePageDetail',
    'tablePage:scrollLoadModeList',
    'tablePage:scrollLoadModeTable',
    'tablePage:extraSearchModel',
    'tablePage:formatSearchModel',
    'tablePage:simpleTablePage',
    'tablePage:tablePageInModal',
    'tablePage:customSearchBtn',

    'nest',
    'error',

    'external',
    'singleSlider',
    'separation',
  ],
  'accessToken': 'aaaa',
  'refreshToken': 'bbbb',
  'expiration': *************,
};

export const userAssistant = {
  'userAccount': 'assistant',
  'userId': ***************,
  'avatar': '',
  'roleId': ***************,
  'permissions': [
    'home',
    'home:index',

    'profile',

    'permission',
    'permission:local',
    'permission:local:btn1',
  ],
  'accessToken': 'cccc',
  'refreshToken': 'dddd',
  'expiration': *************,
};

export const loginMock = [
  http.post<{ userAccount: string }>('/api/user/login', async({ request }) => {
    const body = await request.json() as { userAccount: string };
    const userInfo = body?.userAccount === 'admin' ? userAdmin : userAssistant;
    return HttpResponse.json(axiosRes({
      ...commonInfo,
      ...userInfo,
    }));
  }),
  http.post('/api/user/logout', () => {
    return HttpResponse.json(axiosRes(null));
  }),
];
