export const themeColors = {
  light: {
    '--contexify-menu-bgColor': '#fff',
    '--contexify-separator-color': 'rgba(0,0,0,.2)',
    '--contexify-item-color': '#333',
    '--contexify-activeItem-color': 'var(--console-antd-colorPrimary)',
    '--contexify-activeItem-bgColor': 'var(--console-antd-colorPrimaryBg)',
    '--contexify-rightSlot-color': '#6f6e77',
    '--contexify-activeRightSlot-color': '#fff',
    '--contexify-arrow-color': '#6f6e77',
    '--contexify-activeArrow-color': '#fff',
  },
  dark: {
    '--contexify-menu-bgColor': 'rgba(40,40,40,.98)',
    '--contexify-separator-color': '#4c4c4c',
    '--contexify-item-color': '#fff',
    '--contexify-activeItem-color': 'var(--console-antd-colorPrimary)',
    '--contexify-activeItem-bgColor': 'var(--console-antd-colorPrimaryBg)',
    '--contexify-rightSlot-color': '#6f6e77',
    '--contexify-activeRightSlot-color': '#fff',
    '--contexify-arrow-color': '#6f6e77',
    '--contexify-activeArrow-color': '#fff',
  },
};
