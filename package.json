{"name": "react-antd-console", "version": "1.0.0", "type": "module", "main": "./dist-lib/react-antd-console.js", "module": "./dist-lib/react-antd-console.js", "exports": {".": {"import": "./dist-lib/react-antd-console.js", "require": "./dist-lib/react-antd-console.js"}}, "types": "./dist-lib/react-antd-console.d.ts", "scripts": {"start": "vite --mode localhost", "start:force": "vite --force --mode localhost", "build:dev": "vite build --mode dev", "build:test": "vite build --mode test", "build:uat": "vite build --mode uat", "build:prod": "vite build --mode prod", "build:lib": "vite build --mode lib", "preview": "vite preview --mode prod", "prepare": "husky", "lint:js": "eslint --cache --ext .js,.jsx,.ts,.tsx ./src", "lint:fix": "eslint --fix --cache --ext .js,.jsx,.ts,.tsx ./src", "analysis": "vite-bundle-visualizer --mode prod", "docs:dev": "vitepress dev docs", "docs:build": "vitepress build docs", "docs:preview": "vitepress preview docs"}, "lint-staged": {"src/**/*.{js,jsx,ts,tsx}": "eslint --cache --ext .js,.jsx,.ts,.tsx --max-warnings 0", "examples/**/*.{js,jsx,ts,tsx}": "eslint --cache --ext .js,.jsx,.ts,.tsx --max-warnings 0"}, "dependencies": {"@ant-design/v5-patch-for-react-19": "^1.0.3", "@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@zhangsai/model": "^0.0.3", "admin-search-list": "^1.0.5", "antd": "^5.25.0", "axios": "^1.9.0", "classnames": "^2.5.1", "dayjs": "^1.11.13", "echarts": "^5.6.0", "framer-motion": "^12.10.4", "i18next": "^25.1.2", "immer": "^10.1.1", "nprogress": "^0.2.0", "react": "^19.1.0", "react-contexify": "^6.0.0", "react-dom": "^19.1.0", "react-i18next": "^15.5.1", "react-router": "^7.6.0", "react-router-toolset": "^0.0.9", "react-use": "^17.6.0", "store2": "^2.14.4"}, "devDependencies": {"@types/node": "^22.15.17", "@types/nprogress": "^0.2.3", "@types/react": "^19.1.3", "@types/react-dom": "^19.1.3", "@typescript-eslint/eslint-plugin": "^8.32.0", "@typescript-eslint/parser": "^8.32.0", "@vitejs/plugin-react": "^4.4.1", "autoprefixer": "^10.4.21", "eslint": "8.57.0", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "husky": "^9.1.7", "less": "^4.3.0", "lint-staged": "^15.5.2", "mermaid": "^11.6.0", "msw": "^2.8.0", "typescript": "^5.8.3", "vite": "^6.3.5", "vite-bundle-visualizer": "^1.2.1", "vite-plugin-sitemap": "^0.7.1", "vite-plugin-svg-icons": "^2.0.1", "vite-plugin-svgr": "^4.3.0", "vitepress": "^1.6.3", "vitepress-plugin-mermaid": "^2.0.17"}, "msw": {"workerDirectory": ["public"]}}