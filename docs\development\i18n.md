# 国际化

国际化方案采用 `i18next` 和 `react-i18next`

## 目录结构

```shell
./src/locales
├── en
│   ├── index.ts
│   ├── home.json
│   └── login.json
├── zh-Hans
│   ├── index.ts
│   ├── home.json
│   └── login.json
└── index.ts
```

## 入口

::: code-group

```tsx [src/main.tsx]
enableMocking().then(async() => {
  await i18nInit();
  root.render(<App />);
});
```

```tsx [src/locales/index.ts]
async function i18nInit() {
  if (mounted) return;

  // 使用import()，以使打包时，代码分割
  const en = (await import('./en')).default;
  const zh_Hans = (await import('./zh-Hans')).default;

  const resources = {
    en,
    ['zh_Hans']: zh_Hans,
  };

  i18n
    .use(initReactI18next)
    .init({
      fallbackLng: baseModel.state.language ?? 'zh_<PERSON>',
      resources,
      interpolation: {
        escapeValue: false,
      },
    });

  mounted = true;
}
```

:::

:::tip
构建打包后，国际化相关的配置文件，会被单独分割打包
:::

## 如何新增语言？

- 在 `src/locales/` 下新建文件夹和文件，例如：

::: code-group

```json [src/locales/en/menu.ts]
{
  "累计": "Total"
}
```

:::

- 将新建的入口文件，添加到 `src/locales/index.ts` 的 `resources` 对象中

::: code-group

```ts [src/locales/index.ts]
const en = (await import('./en')).default; // [!code ++]

const resources = {
  ['zh_Hans']: zh_Hans,
  en, // [!code ++]
};
```

:::

## 使用

组件内使用:

```tsx
import { useTranslation } from 'react-i18next'; // [!code focus]

function App() {
  // useTranslation 传入模块名 // [!code focus]
  const { t: t_menu } = useTranslation('menu'); // [!code focus]
  return (
    <div>{t_menu('累计')}</div> // [!code focus]
  );
}

export default App;
```

组件外使用:

```ts
import i18n from '@/locales';

i18n.t(`menu:累计`) // 模块用 : 分隔
```

## 切换语言

```ts
import i18n from '@/locales';

i18n.changeLanguage('zh_Hans');
```

::: tip
语言作为 `baseMode` 的 变量 `language`，定义在 `src/models/base/index.ts` 中
:::
