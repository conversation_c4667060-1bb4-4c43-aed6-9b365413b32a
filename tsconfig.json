{
  "compilerOptions": {
    "target": "esnext",
    "module": "esnext",
    "lib": ["esnext", "dom"],
    "moduleResolution": "node",
    "forceConsistentCasingInFileNames": true,
    "noImplicitReturns": false,
    "ignoreDeprecations": "5.0",
    "noUnusedLocals": true,
    "allowJs": true,
    "importHelpers": true,
    "jsx": "react-jsx",
    "allowSyntheticDefaultImports": true,
    "esModuleInterop": true,
    "sourceMap": true,
    "strict": true,
    "baseUrl": "./",
    "paths": {
      "@/*": ["src/*"],
      "@@/*": ["examples/*"]
    },
    "types": ["vite/client", "vite-plugin-svgr/client", "vite-plugin-svg-icons/client"],
    "resolveJsonModule": true,
    "declaration": true,
    "experimentalDecorators": true,
    "outDir": "./dist",
  },
  "include": [
    "typings.d.ts",
    "mock/**/*",
    "src/**/*",
    "examples/**/*",
    "tests/**/*",
    "test/**/*",
    "__test__/**/*",
    "typings/**/*",
    "config/**/*",
    ".eslintrc.js",
    ".stylelintrc.js",
    ".prettierrc.js",
    "jest.config.js",
  ],
  "exclude": ["node_modules", "dist", "deploy", "build", "jest", ".eslintrc.js", "postcss.config.js"]
}
