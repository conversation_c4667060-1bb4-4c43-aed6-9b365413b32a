<p align="center">
  <img width="320" src="https://static.react-antd-console.site/logo-name.png">
</p>

# Frontend Solution for Admin Dashboard

[🔗Live Preview](https://template.react-antd-console.site) | [📒Documentation](https://doc.react-antd-console.site) | [中文](./README.md) | English

Now supports **React 19**!
Now supports **KeepAlive** (experimental)!

<p align="center">
  <img width="100%" src="https://static.react-antd-console.site/template.png?g=1">
</p>

## Introduction

react-antd-console is a frontend solution for admin dashboards, encapsulating essential features (such as login, authentication, menus, breadcrumbs, tabs, etc.) to help developers focus on rapid business development. The project is based on the latest versions of `React 19`, `Ant Design 5`, `Vite`, and `TypeScript`. The technologies used will be continuously updated to their latest versions.

## Who Is This For?

If you're looking for a minimalist frontend template for admin dashboards with an advanced yet stable tech stack, hoping to effortlessly grasp its principles or focus solely on business development, then this project is for you. It is the culmination of the author's years of experience.

## Keep It Simple

Whether you're using this project for development or learning purposes, simplicity is key. Therefore, this project focuses on:

- Well-designed code hierarchy

- Clear and defined directory structure

- Modular classifications that are easy to modify or replace

This project minimally encapsulates essential features like login, authentication, menus, breadcrumbs, and tabs. If you don’t have your own UI design, you can directly use the built-in UI. If you do have a custom UI design, you can easily adapt this project as a foundation.

## Features

- **🔥 Latest Tech Stack**: `Vite (supports HMR)`, `React 19`, `Ant Design 5`, `TypeScript` (nearly 100% type coverage)
- **🎯 Focus on Business**: Pre-built layouts (side menus, breadcrumbs, tabs, headers, footers, etc.), allowing you to `focus on business development`
-**🔒 Auth Management**: Supports `menu-level` and `button-level` permissions
-**🛠️ Routing Configuration**: A minimalist configuration automatically generates routes, menus, breadcrumbs, etc. Supports nested routes, single/no-layout configurations, and dynamic route changes
- **💾 Data Management**: Layered (data and view) architecture design. The data management solution theoretically supports any UI rendering library/framework (including but not limited to React/Vue/Angular)
- **🎨 Theme Customization**: Supports arbitrary color switching in dark/light modes
- **🏷️ Multi-Tabs**: Draggable multi-tabs with persistence, right-click menus, etc.
- **✨ Keep alive**: Supports page state caching, retaining the page state before switching when returning to the page.
- **🎬 Elegant Animations**: Supports route transition animations, tab, menu, and button animations
- **🧩 Other Features**: `Responsive design`, `internationalization`, `Mock`, `environment configuration`, `engineering standards`, etc.

## Quick Start

```shell
# Install
npm i

# Start
npm start
```

Visit <a href="http://localhost:9527" target="_blank">http://localhost:9527</a> in your browser.

## Build

```shell
# Build for production
npm run build:prod
```

## Browser Compatibility

Compatible with browsers supporting ES2015. Not compatible with IE. Recommended minimum versions:

- Chrome >=87
- Firefox >=78
- Safari >=14
- Edge >=88

## Themes

### Custom Primary Colors

<p align="center">
  <img width="100%" src="https://static.react-antd-console.site/template-colors.png">
</p>

### Yolk

<p align="center">
  <img width="100%" src="https://static.react-antd-console.site/template-yolk.png">
</p>

### Dark

<p align="center">
  <img width="100%" src="https://static.react-antd-console.site/template-dark.png?a=1">
</p>

### Ocean

<p align="center">
  <img width="100%" src="https://static.react-antd-console.site/template-ocean.png">
</p>

### Monokai

<p align="center">
  <img width="100%" src="https://static.react-antd-console.site/template-monokai.png">
</p>

### Cosmic

<p align="center">
  <img width="100%" src="https://static.react-antd-console.site/template-cosmic.png">
</p>

## Multi-Tabs

<p align="center">
  <img width="100%" src="https://static.react-antd-console.site/template-tabs.png?a=1">
</p>

[🔗Live Preview](https://template.react-antd-console.site) | [📒Documentation](https://doc.react-antd-console.site) | [中文](./README.md) | English

## How to Support This Project?

- If you like this project or find it useful, give us a star ⭐️
- Create pull requests, submit issues, suggest new features, or improve the documentation 🔧
