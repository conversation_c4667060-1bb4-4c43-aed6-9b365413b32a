# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

node_modules
dist
dist-lib
dist-version
dist-ssr
*.local

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# dependencies
/node_modules
/package-lock.json
/yarn.lock

# misc
.DS_Store
/coverage
.idea
*bak
.vscode

# visual studio code
.history
*.log
functions/*
.temp/**

# screenshot
screenshot
.firebase
.eslintcache

# docs
docs/.vitepress/dist/*
docs/.vitepress/cache/*

# other

*.local
stats.html
