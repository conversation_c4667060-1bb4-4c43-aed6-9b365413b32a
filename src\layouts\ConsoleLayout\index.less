.console-layout {
  width: 100%;
  height: 100%;
  display: flex;
  background-color: var(--layout-background-color);
}

// 移动端遮罩层
.console-layout__overlay {
  position: fixed;
  inset: 0;
  background: rgba(0, 0, 0, .6);
  z-index: 10;
}

.console-layout__left-side {
  height: 100%;
}
.isMobile .console-layout__left-side {
  overflow: hidden;
  position: fixed;
  z-index: 20;
}

.console-layout__right-side {
  flex: 1;
  min-width: 0; // 防止右侧变大
  width: 100%;
  display: flex;
  flex-direction: column;
  margin-left: var(--layout-gutter);
}
.isMobile .console-layout__right-side {
  margin-left: 0;
}

.console-layout__right-side-tabs {
  display: flex;
  align-items: center;
}

.console-layout__right-side-main-wrap {
  flex: 1;
  // overflow: auto;
  // padding: 24px;
  background-color: var(--container-background-color);
  overflow: auto;
  border-radius: 10px;
  box-shadow: var(--layout-box-shdow);
}

.console-layout__right-side-main {
  width: 100%;
  height: 100%;
  overflow: auto;
  padding: 24px;
  background-color: var(--container-background-color);
}
