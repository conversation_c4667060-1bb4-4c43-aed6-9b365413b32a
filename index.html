<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="shortcut icon" type="image/x-icon" href="/images/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="keywords" content="react antd console,react-antd-console,react管理后台,antd管理后台,react antd管理后台,react antd模板,管理后台,后台管理,后台管理模板,react admin,react antd admin">
    <meta name="description" content="react-antd-console是一个后台管理系统的前端解决方案，封装了后台管理系统必要功能（如登录、鉴权、菜单、面包屑、标签页等），帮助开发人员专注于业务快速开发。项目基于 React19、Ant design5、Vite 和 TypeScript 等新版本。对于使用到的各项技术，会被持续更新至最新版本">
    <title>react-antd-console</title>
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
