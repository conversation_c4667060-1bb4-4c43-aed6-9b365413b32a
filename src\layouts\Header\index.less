@import '@/styles/utils.less';

.console-layout__header {
  display: flex;
  justify-content: space-between;
  width: 100%;
  height: 42px;
  background-color: var(--container-background-color);
  border-radius: var(--layout-border-radius);
  box-shadow: var(--layout-box-shdow);
}
.isMobile .console-layout__header {
  justify-content: end;
}

.console-layout__header-left {
  flex: 1;
  display: flex;
  align-items: center;
  padding-left: 18px;
  overflow-x: auto;
  white-space: nowrap;
  .scroll-bar-none();
}

.console-layout__header-right {
  display: flex;
  align-items: center;
  margin: 0 14px;
}

.console-layout__header-right-icon-wrap {
  display: flex;
  align-items: center;
  height: 100%;
  padding: 0 2px;
  margin-left: 2px;
  .ant-btn {
    width: 28px;
    height: 28px;
  }
}
