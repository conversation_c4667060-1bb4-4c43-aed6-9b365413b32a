import { initReactI18next } from 'react-i18next';
import i18n from 'i18next';
import { baseModel } from '@/models/base';
// import en from './en';
// import zh_<PERSON> from './zh-<PERSON>';

let mounted = false;

export async function i18nInit() {
  if (mounted) return;

  const en = (await import('./en')).default;
  const zh_<PERSON> = (await import('./zh-<PERSON>')).default;

  const resources = {
    en,
    ['zh_<PERSON>']: zh_<PERSON>,
  };

  i18n
    .use(initReactI18next)
    .init({
      fallbackLng: baseModel.state.language ?? 'zh_<PERSON>',
      resources,
      interpolation: {
        escapeValue: false,
      },
    });

  mounted = true;
}

export default i18n;

