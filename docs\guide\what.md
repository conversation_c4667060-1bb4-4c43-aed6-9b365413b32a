# 简介

## react-antd-console 是什么？

react-antd-console 是一个后台管理系统的前端解决方案，封装了后台管理系统必要功能（如登录、鉴权、菜单、面包屑、标签页等），帮助开发人员专注于业务快速开发。项目基于 `React 19`、`Ant design 5`、`Vite` 和 `TypeScript` 等新版本。对于使用到的各项技术，会被持续更新至最新版本

## 谁适合使用？

如果你正在寻找一款极简的后台管理的前端模板，技术栈能先进且稳定，希望毫不费力地掌握其中的原理，或希望只专注于业务开发，那么可以尝试本项目，本项目是作者多年经验的总结

## 尽可能简单

无论是使用本项目做开发，还是学习的目的，保持简单是必要的。因此本项目专注于：良好的代码层次设计、定义清晰明确的目录结构、容易改造和拆换的模块分类等。本项目最小化的封装了一些必要的功能，例如登录、鉴权、菜单、面包屑、标签页等。如果你没有自己的UI设计，那么可以直接使用本项目封装的功能；如果你有自己的UI设计，那么也可以在本项目基础上作方便的改造

## 功能

- **🔥 最新技术栈**: `Vite`(支持`热更新`)、`React19`、`Ant Design5`、`TypeScript`(近乎`100%`的类型覆盖)
- **🎯 专注业务**: 封装好的布局(侧边菜单、面包屑、标签页、页头页脚等)，只需要`专注于业务开发`
- **🔒 权限管理**: 支持`菜单级`和`按钮级`权限
- **🛠️ 路由配置**: 一份极简配置，自动生成路由、菜单、面包屑等，支持嵌套路由、单/无布局等配置，支持路由动态变化等
- **💾 数据管理**: `分层`（数据和视图）架构设计，数据管理方案理论上支持接入任意UI渲染库/框架（包括不限于React/Vue/Angular）
- **🎨 颜色换肤**: 支持深/浅肤色模式下的任意颜色切换
- **🏷️ 多标签页**: 可拖拽的多标签页，支持持久化、右键菜单等
- **✨ 页面缓存**: 支持页面状态缓存，切换回页面后，保留切换前的页面状态
- **🎬 优雅动画**: 支持路由切换动画，标签页、菜单、功能按钮动画等
- **🧩 其他功能**: 如`响应式设计`、`国际化`、`Mock`、`环境配置`、`工程化规范`等

## 浏览器兼容

兼容支持es2015的浏览器，不兼容IE，建议不低于:

- Chrome >=87
- Firefox >=78
- Safari >=14
- Edge >=88
