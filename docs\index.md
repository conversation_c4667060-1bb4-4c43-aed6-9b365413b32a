---
# https://vitepress.dev/reference/default-theme-home-page
layout: home

hero:
  name: "react-antd-console"
  text: "后台管理系统前端解决方案"
  tagline: 简单、专注、高效
  image: https://static.react-antd-console.site/template.png
  actions:
    - theme: brand
      text: 快速开始
      link: /guide/what
    - theme: alt
      text: 在线预览
      link: https://template.react-antd-console.site

features:
  - title: 最新技术栈
    icon: 🔥
    details: 使用 React 19、Ant design 5、TypeScript、Vite 等新版本
  - title: 上手简单
    icon: 🔧
    details: 结构清晰，模块独立，内易修改，外易拆换
  - title: 专注业务
    icon: 🎯
    details: 封装好了登录、鉴权、菜单、面包屑、标签页等功能，只需专注于业务开发
  - title: 主题定制
    icon: 🎨
    details: 支持深/浅肤色模式下的任意颜色切换，以及多种主题风格选择
---
