import menu from './menu.json';
import layout from './layout.json';
import login from './login.json';
import grid from '@/pages/grid/locales/zh-Hans/grid.json';
import permission from '@/pages/permission/locales/zh-Hans/permission.json';
import router from '@/pages/router/locales/zh-Hans/router.json';
import tablePage from '@/pages/tablePage/locales/zh-Hans/tablePage.json';
import error from './error.json';

const zh_<PERSON> = {
  menu,
  layout,
  login,
  grid,
  permission,
  router,
  tablePage,
  error,
};

export default zh_<PERSON>;

