.console-login {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  background-size: cover;
  background-image: url('/images/login_bg.jpg');
}
.isDark .console-login {
  background-image: none;
  background-color: var(--layout-background-color);
}

.console-login__language {
  display: flex;
}

.console-login__tools {
  position: absolute;
  top: 30px;
  right: 30px;
  padding: 8px;
  background-color: rgba(255, 255, 255, 0.76);
  border-radius: 10px;
  box-shadow: 0 1px 8px 0 rgba(0,0,0,.15);
  .ant-space, .ant-space-item {
    display: flex;
  }
  .ant-btn {
    width: 28px;
    height: 28px;
  }
}

.isDark {
  .console-login__tools {
    background-color: var(--container-background-color);
  }
}

.console-login__container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 56px 48px;
  background-color: rgba(255, 255, 255, 0.76);
  border-radius: 24px;
  box-shadow: 0px 4px 10px rgba(0,0,0,0.35);
}
.isDark {
  .console-login__container {
    background-color: var(--container-background-color);
  }
}

.console-login__login-form {
  width: 360px;
  border-radius: 24px;
}

.console-login__login-form-header {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 24px;
  h1 {
    margin-left: 12px;
  }
}

.console-login__login-form-item {
  width: 100%;
  margin-bottom: 24px;
}

.console-login__login-form-register {
  margin-top: 12px;
}

.console-login__footer {
  position: absolute;
  bottom: 0;
  padding: 24px;
  display: flex;
  align-items: center;
  svg {
    margin: 0 4px;
  }
}
