.console-layout-tab__ani-span {
  display: flex;
}

.console-layout-tab {
  position: relative;
  background-color: transparent;

  display: flex;
  align-items: center;
  padding: 4px 8px;
  border-radius: 10px;
  cursor: pointer;
  margin: 0 4px 7px;
  background: var(--tab-bg-color);
  box-shadow: var(--tab-box-shadow);

  &:hover {
    background: var(--tab-bg-hover-color);
    box-shadow: none;
  }

  &.isActive {
    position: relative;
    background-color: var(--tab-bg-active-color);
    box-shadow: 0px 8px 0px 0px var(--tab-bg-active-color), 0 8px 0 0 var(--tab-bg-active-color);
    border-radius: 10px 10px 0 0;
    margin-bottom: 7px;

    &:before, &:after {
      position: absolute;
      bottom: -7px;
      content: '';
      width: 20px;
      height: 20px;
      border-radius: 100%;
      box-shadow: 0 0 0 40px var(--tab-bg-active-color);
    }

    &:before {
      left: -20px;
      clip-path: inset(50% 0 0 50%);
    }
    &:after {
      right: -20px;
      clip-path: inset(50% 50% 0 0);
    }
  }
}

.console-layout-tab__label {
  margin-left: 6px;
}

.console-layout-tab__icon {
  display: flex;
  align-items: center;
}

.console-layout-tab__icon, .console-layout-tab__label, .console-layout-tab__close {
  color: var(--tab-text-color);
}

.isActive {
  .console-layout-tab__icon, .console-layout-tab__label, .console-layout-tab__close {
    color: var(--console-antd-colorPrimary);
  }
}

.console-layout-tab__close {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 18px;
  height: 18px;
  // padding: 2px;
  margin-left: 6px;
  &:hover {
    border-radius: 50%;
    cursor: pointer;
    background-color: var(--tab-bg-hover-color);
  }
  .anticon {
    font-size: 12px;
  }
}
